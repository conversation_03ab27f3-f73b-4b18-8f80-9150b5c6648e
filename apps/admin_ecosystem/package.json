{"name": "admin_ecosystem", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@lds/toolkit": "workspace:*", "@reduxjs/toolkit": "^2.8.2", "date-fns": "^4.1.0", "next": "15.3.3", "next-intl": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@repo/tailwind-config": "workspace:*", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}
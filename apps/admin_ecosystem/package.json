{"name": "admin_ecosystem", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@lds/toolkit": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.3", "next-intl": "^4.1.0"}, "devDependencies": {"@repo/tailwind-config": "workspace:*", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "@eslint/eslintrc": "^3"}}
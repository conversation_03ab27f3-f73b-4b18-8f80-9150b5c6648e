/**
 * Dashboard API Layer
 * Following UI Rules and Backend Documentation
 * Handles dashboard-specific API calls
 */

import { apiClient, ApiResponse } from '@/services/api/client';
import { apiRoutes } from '@/services/api/routes';

// Dashboard data types
export interface DashboardStats {
  totalUsers: number;
  totalOrganizations: number;
  totalContent: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'user_created' | 'content_published' | 'organization_added';
  description: string;
  timestamp: string;
  userId?: string;
  userName?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'error';
  services: {
    gateway: boolean;
    core: boolean;
    database: boolean;
  };
  uptime: number;
  version: string;
}

/**
 * Get dashboard statistics
 * This would typically be a dedicated endpoint, but for now we'll aggregate data
 */
export const getDashboardStats = async (): Promise<ApiResponse<DashboardStats>> => {
  try {
    // In a real implementation, this would be a single endpoint
    // For now, we'll simulate dashboard data
    const mockStats: DashboardStats = {
      totalUsers: 0,
      totalOrganizations: 0,
      totalContent: 0,
      recentActivity: [],
    };

    return {
      data: mockStats,
      type: 'success',
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get system health status
 * GET /api/health
 */
export const getSystemHealth = async (): Promise<ApiResponse<SystemHealth>> => {
  try {
    const response = await apiClient.get<any>(apiRoutes.system.health);

    // Transform response to our format
    const healthData: SystemHealth = {
      status: response.data?.status || 'healthy',
      services: {
        gateway: response.data?.gateway || true,
        core: response.data?.core || true,
        database: response.data?.database || true,
      },
      uptime: response.data?.uptime || 0,
      version: response.data?.version || '1.0.0',
    };

    return {
      ...response,
      data: healthData,
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get recent activity
 * This would typically come from audit logs
 */
export const getRecentActivity = async (): Promise<ApiResponse<ActivityItem[]>> => {
  try {
    // In a real implementation, this would fetch from audit logs
    // GET /api/core/admin/audit-logs with recent filter
    
    const mockActivity: ActivityItem[] = [
      {
        id: '1',
        type: 'user_created',
        description: 'New user registered',
        timestamp: new Date().toISOString(),
        userName: 'John Doe',
      },
      {
        id: '2',
        type: 'content_published',
        description: 'New article published',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
      },
    ];

    return {
      data: mockActivity,
      type: 'success',
    };
  } catch (error) {
    throw error;
  }
};

// Export all dashboard API functions
export const dashboardApi = {
  getDashboardStats,
  getSystemHealth,
  getRecentActivity,
};

export default dashboardApi;

/**
 * Dashboard Redux Slice
 * Following UI Rules - Feature-based Redux slice
 * Manages dashboard state and data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { dashboardApi, DashboardStats, ActivityItem, SystemHealth } from '../@api';

// Dashboard state interface
interface DashboardState {
  stats: DashboardStats | null;
  activities: ActivityItem[];
  systemHealth: SystemHealth | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Initial state
const initialState: DashboardState = {
  stats: null,
  activities: [],
  systemHealth: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const loadDashboardStatsAsync = createAsyncThunk(
  'dashboard/loadStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getDashboardStats();
      
      if (response.error) {
        return rejectWithValue(response.meta?.message || 'Failed to load dashboard stats');
      }
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load dashboard stats');
    }
  }
);

export const loadRecentActivityAsync = createAsyncThunk(
  'dashboard/loadActivity',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getRecentActivity();
      
      if (response.error) {
        return rejectWithValue(response.meta?.message || 'Failed to load recent activity');
      }
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load recent activity');
    }
  }
);

export const loadSystemHealthAsync = createAsyncThunk(
  'dashboard/loadSystemHealth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardApi.getSystemHealth();
      
      if (response.error) {
        return rejectWithValue(response.meta?.message || 'Failed to load system health');
      }
      
      return response.data;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load system health');
    }
  }
);

export const loadAllDashboardDataAsync = createAsyncThunk(
  'dashboard/loadAll',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // Load all dashboard data in parallel
      const results = await Promise.allSettled([
        dispatch(loadDashboardStatsAsync()).unwrap(),
        dispatch(loadRecentActivityAsync()).unwrap(),
        dispatch(loadSystemHealthAsync()).unwrap(),
      ]);

      // Check if any failed
      const failures = results.filter(result => result.status === 'rejected');
      if (failures.length > 0) {
        return rejectWithValue(`Failed to load ${failures.length} dashboard components`);
      }

      return true;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load dashboard data');
    }
  }
);

// Dashboard slice
const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setStats: (state, action: PayloadAction<DashboardStats>) => {
      state.stats = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    setActivities: (state, action: PayloadAction<ActivityItem[]>) => {
      state.activities = action.payload;
    },
    setSystemHealth: (state, action: PayloadAction<SystemHealth>) => {
      state.systemHealth = action.payload;
    },
    addActivity: (state, action: PayloadAction<ActivityItem>) => {
      state.activities.unshift(action.payload);
      // Keep only the latest 10 activities
      if (state.activities.length > 10) {
        state.activities = state.activities.slice(0, 10);
      }
    },
    clearDashboard: (state) => {
      state.stats = null;
      state.activities = [];
      state.systemHealth = null;
      state.error = null;
      state.lastUpdated = null;
    },
  },
  extraReducers: (builder) => {
    // Load dashboard stats
    builder
      .addCase(loadDashboardStatsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadDashboardStatsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stats = action.payload || null;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(loadDashboardStatsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Load recent activity
    builder
      .addCase(loadRecentActivityAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadRecentActivityAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.activities = action.payload || [];
      })
      .addCase(loadRecentActivityAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Load system health
    builder
      .addCase(loadSystemHealthAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(loadSystemHealthAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.systemHealth = action.payload || null;
      })
      .addCase(loadSystemHealthAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Load all dashboard data
    builder
      .addCase(loadAllDashboardDataAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadAllDashboardDataAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(loadAllDashboardDataAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { 
  clearError, 
  setStats, 
  setActivities, 
  setSystemHealth, 
  addActivity, 
  clearDashboard 
} = dashboardSlice.actions;

// Export reducer
export default dashboardSlice.reducer;

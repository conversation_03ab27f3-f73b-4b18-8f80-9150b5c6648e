/**
 * Dashboard Screen Component
 * Following UI Rules - Main screen component
 * Composable screen-level component with proper structure
 */

'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/screens/auth/@context/auth-context';
import { dashboardApi, DashboardStats, ActivityItem, SystemHealth } from './@api';
import StatsCard from './components/stats-card';
import ActivityFeed from './components/activity-feed';
import { Users, Building, FileText, Activity } from 'lucide-react';

const DashboardScreen: React.FC = () => {
  const { user, isLoading: authLoading } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!authLoading && user) {
      loadDashboardData();
    }
  }, [authLoading, user]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load dashboard data in parallel
      const [statsResponse, activitiesResponse, healthResponse] = await Promise.allSettled([
        dashboardApi.getDashboardStats(),
        dashboardApi.getRecentActivity(),
        dashboardApi.getSystemHealth(),
      ]);

      // Handle stats
      if (statsResponse.status === 'fulfilled' && !statsResponse.value.error) {
        setStats(statsResponse.value.data || null);
      }

      // Handle activities
      if (activitiesResponse.status === 'fulfilled' && !activitiesResponse.value.error) {
        setActivities(activitiesResponse.value.data || []);
      }

      // Handle system health
      if (healthResponse.status === 'fulfilled' && !healthResponse.value.error) {
        setSystemHealth(healthResponse.value.data || null);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-2xl font-bold text-gray-900">
              {getGreeting()}, {user?.firstName || user?.username || 'Admin'}!
            </h1>
            <p className="text-gray-600 mt-1">
              Welcome to your Legalyard admin dashboard
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
            <button
              onClick={loadDashboardData}
              className="mt-2 text-red-600 hover:text-red-700 font-medium text-sm"
            >
              Try again
            </button>
          </div>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Total Users"
            value={stats?.totalUsers || 0}
            description="Registered users"
            icon={Users}
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="Organizations"
            value={stats?.totalOrganizations || 0}
            description="Active organizations"
            icon={Building}
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="Content Items"
            value={stats?.totalContent || 0}
            description="Published content"
            icon={FileText}
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="System Status"
            value={systemHealth?.status === 'healthy' ? 'Healthy' : 'Issues'}
            description={`Uptime: ${systemHealth?.uptime || 0}h`}
            icon={Activity}
            className={systemHealth?.status === 'healthy' ? 'border-green-200' : 'border-yellow-200'}
          />
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Activity Feed */}
          <div className="lg:col-span-2">
            <ActivityFeed
              activities={activities}
              isLoading={isLoading}
            />
          </div>

          {/* System Health */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
            {systemHealth ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Gateway</span>
                  <span className={`text-sm font-medium ${systemHealth.services.gateway ? 'text-green-600' : 'text-red-600'}`}>
                    {systemHealth.services.gateway ? 'Online' : 'Offline'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Core Service</span>
                  <span className={`text-sm font-medium ${systemHealth.services.core ? 'text-green-600' : 'text-red-600'}`}>
                    {systemHealth.services.core ? 'Online' : 'Offline'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Database</span>
                  <span className={`text-sm font-medium ${systemHealth.services.database ? 'text-green-600' : 'text-red-600'}`}>
                    {systemHealth.services.database ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <div className="pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Version: {systemHealth.version}
                  </p>
                </div>
              </div>
            ) : (
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardScreen;

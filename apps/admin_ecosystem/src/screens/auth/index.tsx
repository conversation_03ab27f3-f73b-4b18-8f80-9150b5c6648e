"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, Input, Loader } from "@lds/toolkit/ui";
// import { getTranslations } from "next-intl/server";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useFormStatus } from "react-dom";
import { loginInitiateAction } from "./@actions";
import { useActionState } from "react";

function Home() {
  const [state, formAction] = useActionState(loginInitiateAction, null);

  console.log({ state });

  return (
    <>
      {state?.meta?.message && (
        <Alert className="!z-10 mb-6" type={state?.type}>
          {state?.meta?.message}
        </Alert>
      )}
      <form action={formAction} className="flex flex-col gap-3">
        <FormRender />
      </form>
    </>
  );
}

const FormRender = () => {
  const t = useTranslations("HomePage");
  const { pending } = useFormStatus();
  console.log({ pending });

  return (
    <>
      {pending && <Loader big center box />}

      <Input
        id="username"
        type="username"
        name="username"
        autoComplete="email"
        placeholder="e.g. su<PERSON><PERSON>gh, <EMAIL>"
        label={{ name: "Email / Username" }}
        required
      />
      <Input
        id="password"
        name="password"
        type="password"
        autoComplete="current-password"
        placeholder="Enter Password"
        label={{ name: "Password" }}
        required
      />

      <div className="flex items-center justify-between gap-2 ">
        <Link href="#" className="font-semibold text-primary hover:!underline">
          Forgot password?
        </Link>
      </div>
      <Button type="submit" className="mt-4" variant="primary-invert">
        {t("signin.submit")}
      </Button>
    </>
  );
};

export default Home;

"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Button, Input, Loader } from "@lds/toolkit/ui";
import { useTranslations } from "next-intl";
import { useAuth } from "./@context/auth-context";
import { useRouter } from "next/navigation";
import Link from "next/link";

function Home() {
  const { login, isLoading, error, clearError } = useAuth();
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      clearError();
      await login(formData.email, formData.password);

      // Redirect to dashboard on successful login
      router.push('/dashboard');
    } catch (err) {
      // Error is handled by the auth context
      console.error('Login failed:', err);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <>
      {error && (
        <Alert className="!z-10 mb-6" type="error">
          {error}
        </Alert>
      )}
      <form onSubmit={handleSubmit} className="flex flex-col gap-3">
        <FormRender
          formData={formData}
          onInputChange={handleInputChange}
          isLoading={isLoading}
        />
      </form>
    </>
  );
}

interface FormRenderProps {
  formData: {
    email: string;
    password: string;
  };
  onInputChange: (field: string, value: string) => void;
  isLoading: boolean;
}

const FormRender: React.FC<FormRenderProps> = ({ formData, onInputChange, isLoading }) => {
  const t = useTranslations("HomePage");

  return (
    <>
      {isLoading && <Loader big center box />}

      <Input
        id="email"
        type="email"
        name="email"
        autoComplete="email"
        placeholder="e.g. <EMAIL>"
        label={{ name: "Email" }}
        value={formData.email}
        onChange={(e) => onInputChange('email', e.target.value)}
        required
      />
      <Input
        id="password"
        name="password"
        type="password"
        autoComplete="current-password"
        placeholder="Enter Password"
        label={{ name: "Password" }}
        value={formData.password}
        onChange={(e) => onInputChange('password', e.target.value)}
        required
      />

      <div className="flex items-center justify-between gap-2 ">
        <Link href="#" className="font-semibold text-primary hover:!underline">
          Forgot password?
        </Link>
      </div>
      <Button type="submit" className="mt-4" variant="primary-invert" disabled={isLoading}>
        {isLoading ? <Loader /> : t("signin.submit")}
      </Button>
    </>
  );
};

export default Home;

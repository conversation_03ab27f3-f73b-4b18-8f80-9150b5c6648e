"use server";

import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { jwtVerify, SignJWT } from "jose";
import { authApi } from "@/services/api/auth";
import { cookies } from "next/headers";

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET);

export async function setToken(token: string, key: string) {
  if (!token) throw new Error("No token provided");

  const cookieStore = await cookies();

  cookieStore.set(key, token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    path: "/",
    maxAge: 60 * 60 * 24 * 7,
  });
}

export async function getToken(key: string): Promise<string | null> {
  const cookieStore = await cookies();
  const token = cookieStore.get(key)?.value;
  if (!token) return null;

  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload?.access_token as string;
  } catch {
    return null;
  }
}

export async function loginInitiateAction(prevState: unknown, formData: FormData) {
  try {
    console.log("prevState:", prevState);

    const username = formData.get("username");
    const password = formData.get("password");

    console.log("username:", username);
    console.log("password:", password);

    const loginData = {
      email: typeof username === "string" ? username : "",
      password: typeof password === "string" ? password : "",
    };

    const res = await authApi.login(loginData);
    console.log({ res });

    if (res.data?.accessToken) {
      // Store tokens using the new API client
      await setToken(res.data.accessToken, "accessToken");
      await setToken(res.data.refreshToken, "refreshToken");
    }

    return res;
  } catch (error) {
    console.log({ error });
    return error;
  }
}

export async function loginAction(prevState: any, formData: FormData) {
  try {
    console.log("prevState:", prevState);
    const cookieStore = await cookies();
    const token = cookieStore.get("user_access_token")?.value;

    const otp = formData.get("otp");

    console.log("otp:", otp);
    console.log("token:", token);

    // For now, return error as OTP flow needs backend implementation
    const error = {
      error: true,
      meta: {
        message: "OTP verification flow needs backend implementation",
        type: "error"
      }
    };

    return error;
  } catch (error) {
    console.log({ error });
    return error;
  }
}

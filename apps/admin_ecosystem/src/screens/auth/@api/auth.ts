/**
 * Authentication API - Updated to use new centralized API client
 * Following Backend Documentation and UI Rules
 */

import { authApi } from "@/services/api/auth";
import { LoginInitiateType, LoginType } from "../types";

// Updated register function using new API client
export const register = async (payload: {
  email: string;
  password?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
}) => {
  try {
    const response = await authApi.register({
      email: payload.email,
      password: payload.password || '',
      username: payload.username,
      firstName: payload.firstName,
      lastName: payload.lastName,
    });

    return response;
  } catch (error) {
    throw error;
  }
};

// Updated login function using new API client
export const loginInitiate = async (payload: LoginInitiateType) => {
  try {
    // Map legacy payload to new format
    const loginPayload = {
      email: payload.username, // Assuming username is email
      password: payload.password || '', // Ensure password is never undefined
    };

    const response = await authApi.login(loginPayload);
    return response;
  } catch (error) {
    throw error;
  }
};

// For OTP-based login (if still needed)
export const login = async (_payload: LoginType) => {
  try {
    // This might need backend support for OTP verification
    // For now, return error indicating OTP flow needs implementation
    throw new Error('OTP login flow needs backend implementation');
  } catch (error) {
    throw error;
  }
};

// Updated auth functions using new API client
export const userAuth = async () => {
  try {
    const response = await authApi.getCurrentUser();
    return response;
  } catch (error) {
    throw error;
  }
};

export const refreshToken = async () => {
  try {
    // The new API client handles token refresh automatically
    // This function can be used to manually trigger refresh if needed
    const response = await authApi.verifyToken();
    return response;
  } catch (error) {
    throw error;
  }
};

export const logout = async () => {
  try {
    const response = await authApi.logout();
    return response;
  } catch (error) {
    throw error;
  }
};

// New functions using updated API
export const verifyAuth = async () => {
  try {
    const response = await authApi.verifyToken();
    return response;
  } catch (error) {
    throw error;
  }
};

export const initializeAuth = async () => {
  try {
    const user = await authApi.initializeAuth();
    return user;
  } catch (error) {
    throw error;
  }
};

export const isAuthenticated = () => {
  return authApi.isAuthenticated();
};

import { apiRoutes } from "@/services/api/routes";
import apiClient from "@/services/axios/auth-client";
import privateApiClient from "@/services/axios/private-api-client";
import { LoginInitiateType, LoginType } from "../types";

const config = {
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
};

// register
export const register = async (payload: {
  email: string;
  password?: string;
}) => {
  return await apiClient.post(apiRoutes().auth.register, payload, config);
};

// login
export const loginInitiate = async (payload: LoginInitiateType) => {
  return await apiClient.post(apiRoutes().auth.loginInitiate, payload, config);
};

export const login = async (payload: LoginType) => {
  return await apiClient.post(apiRoutes().auth.login, payload, config);
};

// auth start
export const userAuth = async () => {
  return await privateApiClient.get(apiRoutes().auth.me);
};

export const refreshToken = async () => {
  return await privateApiClient.get(apiRoutes().auth.refresh);
};

export const logout = async () => {
  return await privateApiClient.get(apiRoutes().auth.logout);
};
// auth end

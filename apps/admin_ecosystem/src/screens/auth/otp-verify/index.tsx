"use client";

import { <PERSON><PERSON>, Button, Input, Loader } from "@lds/toolkit/ui";
import { OTPInput } from "@lds/toolkit/ui";
import { useTranslations } from "next-intl";
import React, { useActionState } from "react";
import { loginAction } from "../@actions";
import { useFormStatus } from "react-dom";

const OTPVerify = () => {
  const [state, formAction] = useActionState(loginAction, null);

  return (
    <>
      {state?.meta?.message && (
        <Alert className="!z-10 mb-6" type={state?.type}>
          {state?.meta?.message}
        </Alert>
      )}
      <form className="space-y-4" action={formAction}>
        <FormRender />
      </form>
    </>
  );
};
const FormRender = () => {
  const t = useTranslations("HomePage");
  const { pending } = useFormStatus();

  return (
    <>
      <div className="flex flex-col gap-4">
        <OTPInput name="otp" label={{ name: "Enter OTP" }} />
      </div>

      <Button type="submit" className="w-full">
        Submit
      </Button>
    </>
  );
};

export default OTPVerify;

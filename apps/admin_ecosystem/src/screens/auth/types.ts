export interface LoginInitiateType {
  username: string;
  password?: string;
}

export interface LoginType {
  otp: string;
  token?: string;
}

export interface UserType {
  public_id: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  avatar: string;
  status: string;
  user_type: string;
  user_role: string;
  [key: string]: string;
}

export interface AuthType {
  user?: UserType;
  token: string | null;
}

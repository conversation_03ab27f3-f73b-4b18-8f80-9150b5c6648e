import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import { getLocale } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import "./globals.css";

const roboto = Roboto({
  subsets: ["latin"],
  weight: ["100", "300", "400", "500", "700", "900"],
  style: ["normal", "italic"],
  display: "swap",
  variable: "--font-roboto",
});

const teko = Teko({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
  variable: "--font-teko",
});

export const metadata: Metadata = {
  title: "Admin Ecosystem | Legalyard",
  description: "Legalyard",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();
  const rtlLocals = ["ar"];

  return (
    <html lang={locale} dir={rtlLocals?.includes(locale) ? "rtl" : "ltr"}>
      <body className={`${teko.variable} ${roboto.variable} antialiased`}>
        <NextIntlClientProvider>{children}</NextIntlClientProvider>
      </body>
    </html>
  );
}

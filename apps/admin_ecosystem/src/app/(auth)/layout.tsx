import Image from "next/image";
import Logo from "@/assets/branding/logo.svg";

export default async function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <main className="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <div className="w-full flex items-center justify-center">
            <div className="relative max-w-max">
              <Image
                alt="Legalyard"
                src={Logo}
                className="mx-auto h-10 w-auto"
                priority={true}
                quality={100}
                style={{ objectFit: "contain" }}
              />
              <span className="absolute right-0 top-full -translate-y-1 text-xl teko_font">
                Ecosystem
              </span>
            </div>
          </div>
          <h2 className="mt-10 text-center text-2xl/9 font-bold tracking-tight text-gray-900">
            Sign in to your account
          </h2>
        </div>
        <div className="mt-4 sm:mx-auto sm:w-full sm:max-w-sm">{children}</div>
      </main>
    </>
  );
}

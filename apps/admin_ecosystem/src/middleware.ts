/**
 * Next.js Middleware for Route Protection
 * Following UI Rules and Backend Authentication Pattern
 * Handles authentication, authorization, and route guards
 */

import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';

// JWT Secret for token verification
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET);

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/admin',
  '/settings',
  '/users',
  '/organizations',
  '/content',
];

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
];

// Admin-only routes
const ADMIN_ROUTES = [
  '/admin',
  '/users',
  '/organizations',
  '/audit-logs',
];

/**
 * Check if route is protected
 */
function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Check if route is public
 */
function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(route => pathname === route || pathname.startsWith(route));
}

/**
 * Check if route requires admin access
 */
function isAdminRoute(pathname: string): boolean {
  return ADMIN_ROUTES.some(route => pathname.startsWith(route));
}

/**
 * Get token from request cookies or headers
 */
function getTokenFromRequest(request: NextRequest): string | null {
  // Try to get from Authorization header first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try to get from cookies
  const tokenFromCookie = request.cookies.get('accessToken')?.value ||
                         request.cookies.get('user_access_token')?.value;
  
  return tokenFromCookie || null;
}

/**
 * Verify JWT token
 */
async function verifyToken(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * Check if user has admin role
 */
function hasAdminRole(payload: any): boolean {
  const roles = payload?.roles || [];
  return roles.includes('admin') || roles.includes('super_admin');
}

/**
 * Create redirect response
 */
function createRedirect(request: NextRequest, path: string): NextResponse {
  const url = request.nextUrl.clone();
  url.pathname = path;
  
  // Add return URL for post-login redirect
  if (path === '/login' && request.nextUrl.pathname !== '/login') {
    url.searchParams.set('returnUrl', request.nextUrl.pathname);
  }
  
  return NextResponse.redirect(url);
}

/**
 * Main middleware function
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and API routes
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/') ||
    pathname.includes('.') // Static files
  ) {
    return NextResponse.next();
  }

  // Allow public routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  // Check if route requires authentication
  if (isProtectedRoute(pathname)) {
    const token = getTokenFromRequest(request);

    if (!token) {
      console.log('No token found, redirecting to login');
      return createRedirect(request, '/login');
    }

    // Verify token
    const payload = await verifyToken(token);
    if (!payload) {
      console.log('Invalid token, redirecting to login');
      return createRedirect(request, '/login');
    }

    // Check admin routes
    if (isAdminRoute(pathname) && !hasAdminRole(payload)) {
      console.log('Insufficient permissions for admin route');
      return createRedirect(request, '/dashboard');
    }

    // Add user info to headers for server components
    const response = NextResponse.next();
    response.headers.set('x-user-id', payload.sub || '');
    response.headers.set('x-user-roles', JSON.stringify(payload.roles || []));
    
    return response;
  }

  // Default: allow the request
  return NextResponse.next();
}

/**
 * Middleware configuration
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

export default middleware;

/**
 * API Routes Configuration
 * Following Backend Gateway Pattern and Documentation
 * Gateway: http://localhost:8000 -> Core: http://localhost:8001
 */

// Direct Gateway endpoints (no proxy)
const authRoutes = {
  login: "/api/auth/login",
  register: "/api/auth/register",
  refresh: "/api/auth/refresh",
  logout: "/api/auth/logout",
  verify: "/api/auth/verify",
};

// Core endpoints (proxied through Gateway: /api/core/* -> Core /api/*)
const adminRoutes = {
  users: "/api/core/admin/users",
  createUser: "/api/core/admin/users",
  updateUser: (id: string) => `/api/core/admin/users/${id}`,
  deleteUser: (id: string) => `/api/core/admin/users/${id}`,
  tenants: "/api/core/admin/tenants",
  createTenant: "/api/core/admin/tenants",
  permissions: "/api/core/admin/permissions",
  auditLogs: "/api/core/admin/audit-logs",
};

const coderRoutes = {
  content: "/api/core/coder/content",
  createContent: "/api/core/coder/content",
  updateContent: (id: string) => `/api/core/coder/content/${id}`,
  deleteContent: (id: string) => `/api/core/coder/content/${id}`,
  forms: "/api/core/coder/forms",
  createForm: "/api/core/coder/forms",
  themes: "/api/core/coder/themes",
  createTheme: "/api/core/coder/themes",
  newsletter: "/api/core/coder/newsletter",
  createNewsletter: "/api/core/coder/newsletter",
};

const docsRoutes = {
  aiAgentComplete: "/api/core/docs/ai-agent-complete",
  aiOnboarding: "/api/core/docs/ai-onboarding",
  architecture: "/api/core/docs/ai-onboarding/architecture",
  authentication: "/api/core/docs/ai-onboarding/authentication",
  apiReference: "/api/core/docs/ai-onboarding/api-reference",
  integrationPatterns: "/api/core/docs/ai-onboarding/integration-patterns",
  typescriptDefinitions: "/api/core/docs/ai-onboarding/typescript-definitions",
  examples: "/api/core/docs/ai-onboarding/examples",
  requirements: "/api/core/docs/ai-onboarding/requirements",
  testing: "/api/core/docs/ai-onboarding/testing",
  workflow: "/api/core/docs/ai-onboarding/workflow",
  openapi: "/api/core/docs/openapi",
};

// System health endpoints
const systemRoutes = {
  health: "/api/health",
  status: "/api",
};

export const apiRoutes = {
  auth: authRoutes,
  admin: adminRoutes,
  coder: coderRoutes,
  docs: docsRoutes,
  system: systemRoutes,
};

// Legacy support for existing code
export default () => ({
  auth: authRoutes,
  admin: adminRoutes,
  coder: coderRoutes,
  docs: docsRoutes,
  system: systemRoutes,
});

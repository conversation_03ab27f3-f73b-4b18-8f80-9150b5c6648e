export const handleError = (res: any) => {
  try {
    if (res) {
      if (res?.code === "ERR_NETWORK") {
        return {
          error: true,
          message: res?.message ?? "something went wrong",
          status: res?.response?.status ?? "unknown",
          code: res?.code ?? "ERR_NETWORK",
        };
      } else {
        return {
          error: true,
          ...(res?.response?.data?.errors ?? {}),
          message:
            res?.response?.data?.errors?.message ||
            res?.response?.data?.message ||
            "something went wrong",
          status: res?.response?.status ?? "unknown",
          code:
            res?.response?.data?.errors?.errorCode ||
            res?.response?.data?.errorCode,
        };
      }
    } else {
      return {
        error: true,
        message: "something went wrong",
        code: "unknown",
      };
    }
  } catch (err) {
    console.error(err);
    return {
      error: true,
      message: "something went wrong",
      code: "unknown",
    };
  }
};

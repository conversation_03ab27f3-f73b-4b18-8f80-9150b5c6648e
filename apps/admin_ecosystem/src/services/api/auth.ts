/**
 * Authentication API Layer
 * Following Backend Documentation and UI Rules
 * Implements Opaque Token + Refresh Token Pattern
 */

import { apiClient, ApiResponse, AuthTokens, User } from './client';
import { apiRoutes } from './routes';

// Request/Response Types
export interface LoginRequest {
  email: string;
  password: string;
  totpCode?: string; // Optional TOTP for MFA
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
}

export interface RefreshRequest {
  refreshToken: string;
}

export interface VerifyResponse {
  user: User;
  tokenValid: boolean;
}

/**
 * Authentication API Functions
 * All functions follow the Gateway pattern and backend documentation
 */

/**
 * Login user with email/password
 * POST /api/auth/login
 */
export const login = async (credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  try {
    const response = await apiClient.post<LoginResponse>(
      apiRoutes.auth.login,
      credentials
    );

    // Store tokens if login successful
    if (response.data && !response.error) {
      apiClient.setTokens({
        accessToken: response.data.accessToken,
        refreshToken: response.data.refreshToken,
        expiresIn: response.data.expiresIn,
      });
    }

    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Register new user
 * POST /api/auth/register
 */
export const register = async (userData: RegisterRequest): Promise<ApiResponse<User>> => {
  try {
    const response = await apiClient.post<User>(
      apiRoutes.auth.register,
      userData
    );

    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Refresh access token
 * POST /api/auth/refresh
 */
export const refreshToken = async (refreshTokenValue: string): Promise<ApiResponse<AuthTokens>> => {
  try {
    const response = await apiClient.post<AuthTokens>(
      apiRoutes.auth.refresh,
      { refreshToken: refreshTokenValue }
    );

    // Update stored tokens if refresh successful
    if (response.data && !response.error) {
      apiClient.setTokens(response.data);
    }

    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Logout user and invalidate session
 * POST /api/auth/logout
 */
export const logout = async (): Promise<ApiResponse<void>> => {
  try {
    const response = await apiClient.post<void>(apiRoutes.auth.logout);

    // Clear stored tokens regardless of response
    apiClient.clearTokens();

    return response;
  } catch (error) {
    // Clear tokens even if logout request fails
    apiClient.clearTokens();
    throw error;
  }
};

/**
 * Verify current token and get user info
 * GET /api/auth/verify
 */
export const verifyToken = async (): Promise<ApiResponse<VerifyResponse>> => {
  try {
    const response = await apiClient.get<VerifyResponse>(apiRoutes.auth.verify);
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * Get current user info (alias for verify)
 * GET /api/auth/verify
 */
export const getCurrentUser = async (): Promise<ApiResponse<User>> => {
  try {
    const response = await verifyToken();
    
    if (response.data?.user) {
      return {
        ...response,
        data: response.data.user,
      };
    }

    throw new Error('User data not found in verify response');
  } catch (error) {
    throw error;
  }
};

/**
 * Check if user is authenticated
 * Returns boolean without making API call
 */
export const isAuthenticated = (): boolean => {
  const token = apiClient.getAccessToken();
  return !!token;
};

/**
 * Initialize authentication state
 * Call this on app startup to restore session
 */
export const initializeAuth = async (): Promise<User | null> => {
  try {
    if (!isAuthenticated()) {
      return null;
    }

    const response = await getCurrentUser();
    
    if (response.data && !response.error) {
      return response.data;
    }

    return null;
  } catch (error) {
    // Token might be expired, clear it
    apiClient.clearTokens();
    return null;
  }
};

// Export all auth functions
export const authApi = {
  login,
  register,
  refreshToken,
  logout,
  verifyToken,
  getCurrentUser,
  isAuthenticated,
  initializeAuth,
};

export default authApi;

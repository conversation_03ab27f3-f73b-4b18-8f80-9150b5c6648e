import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import classNames from "classnames";

const alertVariants = cva(
  "relative w-full rounded-base border border-gray px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:w-5 [&>svg]:w-5 [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-1/2 [&>svg]:-translate-1/2 [&>svg]:text-font [&>svg~*]:pl-5",
  {
    variants: {
      variant: {
        default: "bg-white text-font",
        destructive:
          "border-gray/50 text-gray dark:border-gray [&>svg]:text-gray",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Alert = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    role="alert"
    className={classNames(alertVariants({ variant }), className)}
    {...props}
  />
));
Alert.displayName = "Alert";

const AlertTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h5
    ref={ref}
    className={classNames(
      "text-start font-medium leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
AlertTitle.displayName = "AlertTitle";

const AlertDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={classNames(
      "text-start text-sm [&_p]:leading-relaxed",
      className
    )}
    {...props}
  />
));
AlertDescription.displayName = "AlertDescription";

export { Alert, AlertTitle, AlertDescription };

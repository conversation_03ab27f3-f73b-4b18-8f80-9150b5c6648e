import { FC, JSX, useEffect, useRef, useState } from "react";
import {
  AlertOctagon,
  AlertTriangle,
  CheckCircle,
  Info,
  X,
} from "react-feather";

import classNames from "classnames";
import { IconRender } from "../icon/render";
import { AlertDescription, <PERSON><PERSON>Title, Alert as <PERSON><PERSON>lert } from "./core";
import { ReactNode } from "react";
import { Icon } from "react-feather";

interface AlertType {
  children?: ReactNode;
  title?: string;
  description?: string | boolean;
  type?: "success" | "error" | "warning" | "info";
  icon?: ReactNode | Icon;
  success?: boolean;
  error?: boolean;
  warning?: boolean;
  info?: boolean;
  autoRemove?: boolean | number;
  onRemove?: () => void | null;
  className?: string;
}

const Alert: FC<AlertType> = ({
  children,
  title,
  description,
  icon,
  type,
  success,
  error,
  warning,
  info,
  autoRemove,
  className,
  onRemove,
}) => {
  const [show, setShow] = useState(false);

  const defaultConfig = (): { icon: JSX.Element; className: string } | null => {
    let config = null;
    if (success || type === "success") {
      config = {
        icon: <CheckCircle className="!text-inherit" />,
        className: "!bg-success/20 !text-success !border-success",
      };
    } else if (error || type === "error") {
      config = {
        icon: <AlertOctagon className="!text-inherit" />,
        className: "!bg-error/20 !text-error !border-error",
      };
    } else if (warning || type === "warning") {
      config = {
        icon: <AlertTriangle className="!text-inherit" />,
        className: "!bg-warning/20  !text-warning !border-warning",
      };
    } else if (info || type === "info") {
      config = {
        icon: <Info className="!text-inherit" />,
        className: "!bg-info/20 !text-info !border-info",
      };
    }

    return config;
  };

  const timeoutId = useRef<NodeJS.Timeout>(undefined);

  useEffect(() => {
    try {
      if (Boolean(children) || Boolean(title) || Boolean(description)) {
        setShow(true);
      } else {
        setShow(false);
      }

      const time = typeof autoRemove === "number" ? autoRemove : 6000;

      if (autoRemove) {
        timeoutId.current = setTimeout(() => {
          setShow(false);
          if (onRemove) {
            onRemove();
          }
        }, time);
      }
    } catch (err) {
      console.error(err);
    }

    return () => {
      clearTimeout(timeoutId.current);
    };
  }, [autoRemove, onRemove, description, title, children]);

  const handleRemove = () => {
    try {
      clearTimeout(timeoutId.current);
      setShow(false);
      if (onRemove) {
        onRemove();
      }
    } catch (err) {
      console.error(err);
    }
  };

  if (!show) {
    return null;
  }

  return (
    <SCAlert
      className={classNames("relative", defaultConfig()?.className, className)}
    >
      {icon ? (
        <IconRender icon={icon} className="!text-inherit" />
      ) : (
        defaultConfig()?.icon
      )}
      <AlertTitle className={classNames(description && "mb-1 ")}>
        {children ?? title}
      </AlertTitle>
      {description && <AlertDescription>{description}</AlertDescription>}
      <button
        className="cursor-pointer hover:text-error rounded-sm absolute right-4 top-1/2 -translate-y-1/2 flex items-center justify-center"
        onClick={() => handleRemove()}
      >
        <X className="w-4 h-4" />
      </button>
    </SCAlert>
  );
};

export { Alert };
export type { AlertType };

import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "../../tooltip";

import classNames from "classnames";
import {
  forwardRef,
  ForwardRefRenderFunction,
  MouseEvent,
  ReactNode,
  Ref,
} from "react";
import { Eye, EyeOff, Key, Search, XCircle } from "react-feather";

interface InputAttributeElementTypes {
  hidden?: boolean;
  src?: string | ReactNode;
  className?: string;
  name?: string;
  tooltip?: {
    hidden?: boolean;
    name?: string | ReactNode;
    direction?: "top" | "right" | "bottom" | "left";
    className?: string;
  };
  onClick?: (d?: boolean) => void;
}

interface InputElementType {
  element?: InputAttributeElementTypes;
  inputType?: string;
  focused?: boolean;
  passShow?: boolean;
  passwordViewToggle?: boolean;
  value?: boolean;
  prefix?: boolean;
  suffix?: boolean;
}

const InputElementRender: ForwardRefRenderFunction<
  HTMLButtonElement,
  InputElementType
> = (
  {
    element,
    inputType,
    focused,
    passShow,
    passwordViewToggle,
    value,
    prefix,
    suffix,
  },
  ref: Ref<HTMLButtonElement>
) => {
  const handleClick = (
    e: MouseEvent<HTMLButtonElement | HTMLDivElement | HTMLImageElement>
  ) => {
    try {
      e?.stopPropagation();
      if (element?.onClick) {
        element?.onClick();
      }
    } catch (error) {
      console.error(error);
    }
  };

  const isPassShowToggle =
    inputType === "password" && suffix && passwordViewToggle;
  const passToggleName = passShow ? "Hide" : "Show";

  if (element?.hidden) {
    return null;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            ref={ref}
            className={classNames(
              "input-element max-w-max h-auto cursor-pointer flex items-center justify-center border-0 outline-0 bg-transparent",
              suffix ? "mr-1.5 ml-0.1" : "ml-1.5 mr-0.1",
              element?.className
            )}
            onClick={(e) => handleClick(e)}
            type="button"
          >
            {typeof element?.src === "string" ? (
              <img
                src={element?.src ? element?.src : ""}
                alt={element?.name ?? "input-icon"}
                className="w-3.5 h-auto"
              />
            ) : (
              <>
                {inputType === "password" &&
                !element?.src &&
                (prefix || suffix) ? (
                  <>
                    {suffix && passwordViewToggle ? (
                      passShow ? (
                        <Eye size={16} />
                      ) : (
                        <EyeOff size={16} />
                      )
                    ) : (
                      prefix && <Key />
                    )}
                  </>
                ) : inputType === "search" && !element?.src ? (
                  <>{suffix && value ? <XCircle /> : prefix && <Search />}</>
                ) : (
                  element?.src
                )}
              </>
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent
          side={element?.tooltip?.direction || "top"}
          align="center"
          className={classNames(
            "z-[1100] text-inherit [&>svg]:w-4 [&>svg]:h-4 [&>svg]:transition-colors hover:[&>svg]:text-primary",
            focused ? "[&>svg]:text-primary" : "[&>svg]:text-grey",
            element?.tooltip?.className
          )}
          hidden={
            isPassShowToggle
              ? false
              : !element?.tooltip || element?.tooltip?.hidden
          }
        >
          {isPassShowToggle ? passToggleName : element?.tooltip?.name}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const InputElement = forwardRef(InputElementRender);
InputElement.displayName = "InputElement";
export { InputElement };
export type { InputAttributeElementTypes, InputElementType };

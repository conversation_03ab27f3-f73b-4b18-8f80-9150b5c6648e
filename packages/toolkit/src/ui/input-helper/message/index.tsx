import classNames from "classnames";
import { FC, InputHTMLAttributes, ReactNode } from "react";
import { AlertOctagon, AlertTriangle, CheckCircle, Info } from "react-feather";

interface InputMessageType extends InputHTMLAttributes<HTMLDivElement> {
  children?: ReactNode;
  className?: string;
  type?: "error" | "success" | "warning" | "info";
}

const InputMessage: FC<InputMessageType> = ({
  children,
  className,
  type,
  ...rest
}) => {
  const defaultIcon =
    type === "success" ? (
      <CheckCircle className="w-3 h-3" />
    ) : type === "error" ? (
      <AlertOctagon className="w-3 h-3" />
    ) : type === "warning" ? (
      <AlertTriangle className="w-3 h-3" />
    ) : type === "info" ? (
      <Info className="w-3 h-3" />
    ) : null;

  return (
    <div
      id="input-message"
      className={classNames(
        "input-message text-sm my-2 flex items-center justify-start gap-x-1",
        {
          "text-font": !type,
          "text-error": type === "error",
          "text-success": type === "success",
          "text-warning": type === "warning",
          "text-info": type === "info",
        },
        className
      )}
      {...rest}
    >
      {defaultIcon}
      {children}
    </div>
  );
};

InputMessage.displayName = "InputMessage";
export { InputMessage };
export type { InputMessageType };

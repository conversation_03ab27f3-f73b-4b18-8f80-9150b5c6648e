import classNames from "classnames";
import {
  forwardRef,
  ForwardRefRenderFunction,
  InputHTMLAttributes,
  Ref,
} from "react";
import "./styles.css";

interface InputBoxType extends InputHTMLAttributes<HTMLInputElement> {
  className?: string;
}

const InputBoxRender: ForwardRefRenderFunction<
  HTMLInputElement,
  InputBoxType
> = ({ className, ...rest }, ref: Ref<HTMLInputElement>) => {
  return (
    <input
      ref={ref}
      className={classNames(
        "input-box bg-transparent border-none text-font text-sm p-1.5 block w-full h-full transition-all rounded-base box-border",
        "focus:border-none focus:border-0 focus:outline-0 active:border-none active:border-0 active:outline-0 focus-within:border-none focus-within:border-0 focus-within:outline-0 focus-visible:border-none focus-visible:border-0 focus-visible:outline-0",
        "disabled:bg-grey disabled:bg-opacity-10 disabled:shadow-inner",
        "placeholder:text-sm",
        className
      )}
      {...rest}
    />
  );
};

const InputBox = forwardRef(InputBoxRender);
export { InputBox };
export type { InputBoxType };

import classNames from "classnames";
import { FC } from "react";

interface InputLabelType {
  name?: string;
  className?: string;
  dynamic?: boolean;
  focused?: boolean;
  focused_className?: string;
}

const InputLabel: FC<InputLabelType> = ({
  name,
  dynamic,
  focused,
  className,
  focused_className,
}) => {
  return (
    <label
      className={classNames(
        "input-label text-font text-sm font-semibold block ml-1 mb-1.5 capitalize",
        dynamic &&
          "absolute top-1/2 left-1 ml-0 -translate-y-1/2 z-10 text-sm font-normal bg-transparent text-gray select-none pointer-events-none p-0.5 leading-tight transition-all text-ellipsis overflow-hidden whitespace-nowrap",
        dynamic &&
          focused &&
          "-top-[0.03rem] left-1 text-xs font-semibold bg-white text-primary",
        dynamic && focused && focused_className,
        className
      )}
    >
      {name}
    </label>
  );
};
InputLabel.displayName = "InputLabel";
export { InputLabel };
export type { InputLabelType };

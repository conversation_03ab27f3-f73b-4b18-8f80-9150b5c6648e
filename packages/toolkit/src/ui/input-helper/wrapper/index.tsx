import classNames from "classnames";
import {
  forwardRef,
  ForwardRefRenderFunction,
  HTMLAttributes,
  ReactNode,
  Ref,
} from "react";
import "./styles.css";

interface InputWrapperType extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
  focused?: boolean;
  error?: boolean;
  valid?: boolean;
  noBOrder?: boolean;
}

const InputWrapperRender: ForwardRefRenderFunction<
  HTMLDivElement,
  InputWrapperType
> = (
  { children, className, focused, error, valid, noBOrder, ...rest },
  ref: Ref<HTMLDivElement>
) => {
  return (
    <div
      ref={ref}
      aria-label="Input Wrapper"
      className={classNames(
        "input-wrapper w-full relative flex items-center bg-white rounded-base h-input border border-solid border-gray px-0.5 py-0 transition-all",
        error && "!border-error",
        valid && "!border-success",
        focused && "!border-primary focused",
        noBOrder && "!border-transparent",
        className
      )}
      {...rest}
    >
      {children}
    </div>
  );
};
const InputWrapper = forwardRef(InputWrapperRender);
InputWrapper.displayName = "InputWrapper";
export { InputWrapper };
export type { InputWrapperType };

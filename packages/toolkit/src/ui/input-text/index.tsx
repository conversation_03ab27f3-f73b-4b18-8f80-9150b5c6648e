"use client";

import classNames from "classnames";
import {
  FocusEvent,
  forwardRef,
  ForwardRefRenderFunction,
  InputHTMLAttributes,
  Ref,
  useState,
} from "react";
import { CountrySelector } from "../country-selector";
import {
  InputAttributeElementTypes,
  InputElement,
} from "../input-helper/element";
import { InputBox } from "../input-helper/input";
import { InputLabel, InputLabelType } from "../input-helper/label";
import { InputMessage } from "../input-helper/message";
import { InputWrapper } from "../input-helper/wrapper";
import { CountrySelectorType } from "../country-selector";

interface InputAttributesTypes {
  prefix?: {
    element: InputAttributeElementTypes;
    className?: string;
  };
  suffix?: {
    element: InputAttributeElementTypes;
    className?: string;
  };
}

interface InputFieldTypes extends InputHTMLAttributes<HTMLInputElement> {
  label?: InputLabelType;
  attributes?: InputAttributesTypes;
  passwordViewToggle?: boolean;
  countrySelector?: CountrySelectorType;
  error?: string;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  onBlur?: (e: FocusEvent<HTMLInputElement>) => void;
  type?: string;
  value?: string;
  className?: string;
  containerClassName?: string;
  wrapperClassName?: string;
}

const InputField: ForwardRefRenderFunction<
  HTMLInputElement,
  InputFieldTypes
> = (props, ref: Ref<HTMLInputElement>) => {
  const [passShow, setPassShow] = useState<boolean>(false);
  const [focused, setFocused] = useState<boolean>(false);

  const {
    label,
    attributes,
    passwordViewToggle,
    countrySelector,
    error,
    onFocus,
    onBlur,
    type,
    value,
    className,
    containerClassName,
    wrapperClassName,
    ...rest
  } = props;

  const PassToggle = () => {
    setPassShow(!passShow);
  };

  const getFocused = (e: FocusEvent<HTMLInputElement>) => {
    if (onFocus) {
      onFocus(e);
    }
    setFocused(true);
  };

  const getBlurred = (e: FocusEvent<HTMLInputElement>) => {
    if (onBlur) {
      onBlur(e);
    }
    setFocused(false);
  };

  const newType = type === "password" && passShow ? "text" : type;

  return (
    <div
      aria-label="Input Field"
      className={classNames("input relative w-full my-1", containerClassName)}
    >
      {Boolean(label) && !label?.dynamic && (
        <InputLabel {...(label ?? {})} focused={focused} />
      )}

      <InputWrapper
        error={Boolean(error)}
        valid={Boolean(value) && !error}
        focused={focused}
        className={wrapperClassName}
      >
        {attributes?.prefix?.element && (
          <InputElement
            inputType={type}
            focused={focused}
            element={attributes?.prefix?.element}
            prefix
          />
        )}

        {countrySelector && (
          <div className="h-full">
            <CountrySelector {...countrySelector} />
          </div>
        )}

        <div className="relative w-full h-full">
          {Boolean(label?.dynamic) && (
            <InputLabel {...(label ?? {})} focused={focused} />
          )}
          <InputBox
            ref={ref}
            onFocus={(e) => getFocused(e)}
            onBlur={(e) => getBlurred(e)}
            value={value}
            type={newType}
            className={className}
            {...rest}
          />
        </div>

        {attributes?.suffix?.element && (
          <InputElement
            inputType={type}
            focused={focused}
            passShow={passShow}
            passwordViewToggle={passwordViewToggle}
            value={Boolean(value)}
            element={{
              ...attributes?.suffix?.element,
              onClick: () => {
                try {
                  if (type === "password") {
                    PassToggle();
                    if (attributes?.suffix?.element?.onClick) {
                      attributes?.suffix?.element?.onClick(!passShow);
                    }
                  } else if (attributes?.suffix?.element?.onClick) {
                    attributes?.suffix?.element?.onClick();
                  }
                } catch (error) {
                  console.error(error);
                }
              },
            }}
            suffix
          />
        )}
      </InputWrapper>

      {error && <InputMessage type="error">{error}</InputMessage>}
    </div>
  );
};

const Input = forwardRef(InputField);
Input.displayName = "Input";
export { Input };
export type { InputAttributesTypes, InputFieldTypes };

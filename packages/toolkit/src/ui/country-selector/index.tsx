"use client";

import {
  FC,
  InputHTMLAttributes,
  RefObject,
  useEffect,
  useRef,
  useState,
} from "react";
import { countryCodes } from "../../constants/country-codes";
import Dropdown from "./component/dropdown";
import SelectBox from "./component/select-box";
import useOutsideAlert from "../../hooks/useOutsideAlert";

interface CountrySelectorOnchangeType {
  target: {
    value: string;
    type: string;
    name: string;
  };
}

interface CountrySelectorType
  extends Omit<InputHTMLAttributes<HTMLSelectElement>, "value"> {
  onChange?: (d: CountrySelectorOnchangeType) => void;
  value?: string;
  defaultValue?: string;
}

const CountrySelector: FC<CountrySelectorType> = ({
  onChange,
  value,
  defaultValue,
  name,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  const [selected, setSelected] = useState<string>(value ?? defaultValue ?? "");
  const [open, setOpen] = useState<boolean>(false);
  const [search, setSearch] = useState<string>("");
  const [resultCount, setResultCount] = useState<number>(5);

  useEffect(() => {
    if (value) {
      setSelected(value);
    }
  }, [value]);

  const handleSelect = (val: string) => {
    try {
      const value = val?.toString();
      if (onChange) {
        onChange({
          target: {
            value: val,
            type: "dialCode",
            name: name || "countryCodes",
          },
        });
      }
      setSelected(value);
      setSearch("");
      setOpen(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleOpen = () => {
    setOpen(!open);
  };

  useOutsideAlert(containerRef, () => setOpen(false));

  const countryList = countryCodes.filter((item) => item.isoCode !== "IN");

  const filterResult = countryList.filter(
    (item) =>
      item.dialCode?.toLowerCase().includes(search?.toLowerCase()) ||
      item.name?.toLowerCase().includes(search?.toLowerCase()) ||
      item.isoCode?.toLowerCase().includes(search?.toLowerCase())
  );

  const finalList = (): typeof countryList => {
    try {
      if (selected) {
        return countryList?.filter((item) => item?.dialCode !== selected);
      } else {
        return countryList;
      }
    } catch {
      return [];
    }
  };

  const renderList = search ? filterResult : finalList();

  const PropsGroup = {
    selected,
    open,
    search,
    setSearch,
    handleSelect,
    resultCount,
    setResultCount,
    renderList,
  };

  return (
    <div
      ref={containerRef}
      aria-label="Country Selector"
      className="relative select-none h-full flex items-center justify-center"
    >
      <SelectBox handleOpen={handleOpen} selected={selected} />

      {open && containerRef?.current !== null && (
        <Dropdown
          {...PropsGroup}
          containerRef={containerRef as RefObject<HTMLDivElement>}
        />
      )}
    </div>
  );
};

CountrySelector.displayName = "CountrySelector";
export { CountrySelector };
export type { CountrySelectorOnchangeType, CountrySelectorType };

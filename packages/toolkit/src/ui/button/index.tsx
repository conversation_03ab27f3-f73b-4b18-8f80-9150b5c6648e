import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import classNames from "classnames";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-white text-dark shadow hover:bg-primary/20 border border-primary/20",
        primary:
          "bg-white hover:bg-primary text-primary hover:text-white border border-primary shadow-sm",
        "primary-invert":
          "bg-primary hover:bg-white text-white hover:text-primary border border-primary shadow-sm",
        destructive:
          "bg-slate-200 text-destructive-foreground shadow-sm hover:bg-stone-200",
        outline:
          "border border-dark text-font bg-transparent shadow-sm hover:bg-slate-50",
        "ghost-brown":
          "bg-brown text-white shadow-sm hover:bg-white hover:text-brown border border-brown",
        "ghost-brown-invert":
          "bg-white text-brown shadow-sm hover:bg-brown hover:text-white border border-brown",
        ghost:
          "bg-black text-white hover:bg-white hover:text-font border border-black",
        input: "bg-white hover:bg-slate-50 border border-light",
        link: "text-primary underline-offset-4 hover:underline",
        plain: "bg-transparent text-font hover:shadow",
      },
      size: {
        default: "h-input px-4 py-1.5",
        sm: "h-7 px-3.5 text-xs",
        lg: "h-10 px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={classNames(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };

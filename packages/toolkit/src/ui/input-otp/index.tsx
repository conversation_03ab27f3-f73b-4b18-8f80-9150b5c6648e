"use client";

import classNames from "classnames";
import { OTPInputProps } from "input-otp";
import { FC, useState } from "react";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "./core";
import { InputLabel, InputLabelType } from "../input-helper/label";
import { InputMessage } from "../input-helper/message";

type InputTargetType = {
  target: {
    value: string;
    name: string;
  };
};

interface OTPInputType
  extends Omit<
    OTPInputProps,
    "render" | "maxLength" | "className" | "onChange"
  > {
  containerClassName?: string;
  className?: {
    otpContainer?: string;
  };
  maxLength?: number;
  name: string;
  label?: InputLabelType;
  error?: string;
  onChange?: (event: InputTargetType) => void;
}

const OTPInput: FC<OTPInputType> = ({
  containerClassName,
  name,
  className,
  maxLength = 6,
  label,
  error,
  onChange,
  ...rest
}) => {
  const [focused, setFocused] = useState<boolean>(false);

  const handleOnchange = (value: string) => {
    if (onChange) {
      onChange({ target: { value, name } });
    }
  };

  return (
    <div
      aria-label="Input Field"
      className={classNames("input relative w-full my-1", containerClassName)}
    >
      {Boolean(label) && !label?.dynamic && (
        <InputLabel {...(label ?? {})} focused={focused} />
      )}
      <InputOTP
        maxLength={maxLength}
        containerClassName={classNames("!gap-4", className?.otpContainer)}
        autoFocus
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        onChange={(value) => handleOnchange(value)}
        {...(rest ?? {})}
      >
        {[...Array(+maxLength).keys()]?.map((_, index) => {
          return (
            <InputOTPGroup key={index} className="flex-1 justify-center">
              <InputOTPSlot
                index={index}
                className="!h-12 !min-w-10 !w-full !flex-1"
              />
            </InputOTPGroup>
          );
        })}
      </InputOTP>
      {error && <InputMessage type="error">{error}</InputMessage>}
    </div>
  );
};

OTPInput.displayName = "OTPInput";
export { OTPInput };
export type { OTPInputType, InputTargetType };

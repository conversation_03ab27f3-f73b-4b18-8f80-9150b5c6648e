/* eslint-disable @typescript-eslint/no-explicit-any */
import classNames from "classnames";
import { createElement, FC, isValidElement, ReactNode } from "react";
import { Icon } from "react-feather";
import Placeholder from "@/assets/placeholder/image.png";
import { Image } from "../../image";

const isIconComponent = (icon: unknown): icon is Icon => {
  return typeof icon === "function" || (isValidElement(icon) && !!icon.type);
};

interface IconRenderType {
  title?: string;
  icon: string | ReactNode | Icon;
  className?: string;
}

const IconRender: FC<IconRenderType> = ({ title, icon, className }) => {
  const renderIcon = (icon: any, props?: any) => {
    try {
      const IconComponent = icon;
      return createElement(IconComponent, props);
    } catch {
      return "";
    }
  };

  if (!icon) return null;

  return (
    <>
      {typeof icon === "string" ? (
        <Image
          src={icon ?? Placeholder}
          alt={title ?? "icon"}
          className={classNames(
            "w-full max-w-max h-auto aspect-square bg-slate-100",
            className
          )}
        />
      ) : isValidElement(icon) ? (
        <>{icon}</>
      ) : isIconComponent(icon) ? (
        <>{renderIcon(icon as Icon, { className, title })}</>
      ) : (
        <>{renderIcon(icon as ReactNode, { className, title })}</>
      )}
    </>
  );
};

IconRender.displayName = "IconRender";
export { IconRender };
export type { IconRenderType };

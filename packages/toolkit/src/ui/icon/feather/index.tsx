import { FC, Suspense, lazy } from "react";

interface FeatherIconType {
  name: string;
}
const FeatherIcon: FC<FeatherIconType> = ({ name, ...rest }) => {
  const IconComponent = lazy(
    () => import(`react-feather/dist/icons/${name.toString()}.js`)
  );

  return (
    <Suspense fallback={null}>
      <IconComponent size="1em" {...rest} />
    </Suspense>
  );
};

FeatherIcon.displayName = "FeatherIcon";
export { FeatherIcon };

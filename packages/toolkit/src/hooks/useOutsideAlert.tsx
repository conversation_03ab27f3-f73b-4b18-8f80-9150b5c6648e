/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { RefObject, useEffect } from "react";

const useOutsideAlert = <T extends HTMLElement>(
  ref?: RefObject<T | null>,
  callback?: (data: any, v?: boolean) => void,
  deactivate?: boolean
) => {
  useEffect(() => {
    if (!deactivate) {
      const handleClickOutside = (event: any) => {
        try {
          event?.stopPropagation();

          if (ref && ref.current && !ref.current.contains(event?.target)) {
            if (callback) {
              callback(event, false);
            }
          }
        } catch (error) {
          console.error(error);
        }
      };

      const handleEscPress = (event: KeyboardEvent) => {
        if (event.key === "Escape") {
          callback?.(event, false);
        }
      };

      try {
        document.addEventListener("keydown", handleEscPress);
        document.addEventListener("mousedown", handleClickOutside);
        document.addEventListener("beforeunload", handleClickOutside);

        return () => {
          document.removeEventListener("keydown", handleEscPress);
          document.removeEventListener("mousedown", handleClickOutside);
          document.removeEventListener("beforeunload", handleClickOutside);
        };
      } catch (error) {
        console.error({ error });
        return () => {
          document.removeEventListener("keydown", handleEscPress);
          document.removeEventListener("mousedown", handleClickOutside);
          document.removeEventListener("beforeunload", handleClickOutside);
        };
      }
    }
  }, [ref, callback, deactivate]);
};

export default useOutsideAlert;

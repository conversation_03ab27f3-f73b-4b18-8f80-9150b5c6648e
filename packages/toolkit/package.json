{"name": "@lds/toolkit", "version": "0.0.0", "private": true, "exports": {".": "./src/index.ts", "./*": "./src/*.tsx", "./ui": {"import": "./src/ui/index.ts"}, "./ui/button": {"import": "./src/ui/button/index.tsx"}, "./globals.css": "./src/globals.css"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/tailwind-config": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9.28.0", "typescript": "5.8.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}
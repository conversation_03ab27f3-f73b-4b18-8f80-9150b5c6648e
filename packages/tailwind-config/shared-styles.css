@import "tailwindcss";

@theme {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --background: #ffffff;
  --foreground: #171717;

  --color-primary: #e27735;
  --color-orange: #eda44c;
  --color-skyBlue: #65bbe9;
  --color-sub: #303030;
  --color-dark: #292929;
  --color-info: #4978d2;
  --color-error: #ed4c63;
  --color-success: #58c6a2;
  --color-warning: #eddc4c;
  --color-black: #000000;
  --color-white: #ffffff;
  --color-gray: #949494;
  --color-grey: #49576f;
  --color-page: #f1f1f4;
  --color-font: #1f1d1d;

  --spacing-headerCut: calc(100vh - 102px);
  --spacing-input: 2.25rem;
  --spacing-header: 108px;
  --spacing-header-md: 97px;
  --spacing-header-sm: 80.06px;

  --radius-base: 0.3125rem;

  --shadow-box: 0px -3px 16px -2px rgba(0, 0, 0, 0.1);
  --shadow-box-sm: 0px 0px 8px -2px rgba(0, 0, 0, 0.1);
  --shadow-box-xs: 0px 0px 4px -2px rgba(0, 0, 0, 0.1);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-caret-blink: caret-blink 1.25s ease-out infinite;
  --animate-swing: swing 2s ease-out infinite;
  --animate-blink: blink 1s linear infinite;
  --animate-shimmer: shimmer 2s linear infinite;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes caret-blink {
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes caret-blink {
    0%,
    70%,
    100% {
      opacity: 1;
    }
    20%,
    50% {
      opacity: 0;
    }
  }

  @keyframes swing {
    20% {
      transform: rotate3d(0, 0, 1, 15deg);
    }
    40% {
      transform: rotate3d(0, 0, 1, -10deg);
    }
    60% {
      transform: rotate3d(0, 0, 1, 5deg);
    }
    80% {
      transform: rotate3d(0, 0, 1, -5deg);
    }
    100% {
      transform: rotate3d(0, 0, 1, 0deg);
    }
  }

  @keyframes blink {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-teko: var(--font-teko);
  --font-roboto: var(--font-roboto);
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;

  @media (width >= theme(--breakpoint-sm)) {
    padding-inline: 2rem;
  }
}

/* CUSTOM CLASSES */
@utility fold {
  @apply relative transition-all duration-150 ease-in-out;

  clip-path: polygon(
    0 0,
    calc(100% - 2.1rem) 0,
    100% 2.1rem,
    100% 100%,
    0 100%
  );

  &::before {
    content: "";
    @apply absolute top-0 right-0 block bg-transparent transition-all duration-150 ease-in-out;
    border-width: 0 2rem 2rem 0;
    border-style: solid;
    border-color: transparent transparent #e27735 #e27735;
    border-radius: 0.05rem;
    box-shadow:
      0 1px 1px rgba(0, 0, 0, 0.3),
      -1px 1px 1px rgba(0, 0, 0, 0.2);
    width: 0;
  }
}

@utility fold-none {
  clip-path: polygon(0 0, 100% 0, 100% 0, 100% 100%, 0 100%);

  &::before {
    content: "";
    border-width: 0;
  }
}

@utility fold-xs {
  clip-path: polygon(
    0 0,
    calc(100% - 0.76rem) 0,
    100% 0.76rem,
    100% 100%,
    0 100%
  );

  &::before {
    border-width: 0 0.75rem 0.75rem 0;
  }
}

@utility fold-sm {
  clip-path: polygon(
    0 0,
    calc(100% - 1.1rem) 0,
    100% 1.1rem,
    100% 100%,
    0 100%
  );

  &::before {
    border-width: 0 1rem 1rem 0;
  }
}
@utility fold-lg {
  clip-path: polygon(
    0 0,
    calc(100% - 3.6rem) 0,
    100% 3.6rem,
    100% 100%,
    0 100%
  );

  &::before {
    border-width: 0 3.5rem 3.5rem 0;
  }
}

@utility click {
  @apply active:scale-75 transition-all;
}

/* CUSTOM CLASSES */

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100%;
  height: 100%;
  font-family: var(--font-roboto), sans-serif;
  background-color: var(--background);
  color: var(--color-font);
  line-height: 1.5;
  @apply text-sm;
}

/* Buttons and links */
a {
  text-decoration: none;
  cursor: pointer;
}

button {
  cursor: pointer;
}

[role="button"] {
  cursor: pointer;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Forms */
input,
select,
textarea {
  outline: none;
}

/* Remove tap highlight on mobile */
button,
a,
input,
textarea,
select {
  -webkit-tap-highlight-color: transparent;
}

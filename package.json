{"name": "legalyard", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.4", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "input-otp": "^1.4.2", "jose": "^6.0.11", "lucide-react": "^0.513.0", "next-intl": "^4.1.0", "react-feather": "^2.0.10", "react-icons": "^5.5.0"}}
Here's a **clear, detailed set of UI development rules and standards** you can give to **Augment Code** or any AI coding agent, to build a **clean, scalable, production-grade Next.js frontend** for your SaaS admin system. These are tailored specifically for **API-driven development**, **performance**, **animations**, and **long-term scalability**.

---

## ✅ UI Architecture & Coding Standards for Next.js (w/ Augment Code)

---

### 1. 📁 **Folder & File Structure Guidelines**

#### 🧩 Component & Page Organization (Example folder structure)

```
/src
├── app/                            # ⚡ Next.js App Router – only for routing & page layout
│   ├── (auth)/login/page.tsx
│   ├── (auth)/register/page.tsx
│   ├── dashboard/page.tsx
│   ├── dashboard/layout.tsx
│   ├── api/                        # Next.js API routes (optional)
│   └── globals.css

├── screens/                        # 💡 Main application logic, UI & features
│   ├── dashboard/
│   │   ├── @redux/                 # Redux slice + hooks for this feature
│   │   ├── @api/                   # API functions (REST/GraphQL)
│   │   ├── @context/               # React Context (if needed)
│   │   ├── components/
│   │   │   ├── sidebar/
│   │   │   │   index.tsx
│   │   │   │   styles.module.css
│   │   │   ├── header/
│   │   │   │   index.tsx
│   │   │   │   motion.tsx
│   │   ├── utils/                  # Local utility functions (optional)
│   │   ├── types/                  # Local TS types & interfaces
│   │   └── screen.tsx             # Composable screen-level component
│
│   ├── login/
│   │   ├── @api/
│   │   ├── components/
│   │   └── screen.tsx
│
│   ├── settings/
│   │   ├── @redux/
│   │   ├── @api/
│   │   ├── components/
│   │   └── screen.tsx
│
│   └── ...more features...

├── public/                         # Static assets
│   └── logo.svg

├── styles/                         # Global Tailwind, base styles
│   ├── globals.css
│   └── tailwind.config.ts

├── middleware.ts                   # Route guards, auth middleware
├── env.d.ts                        # Env type definitions
├── next.config.js
└── tsconfig.json

#### screens/ Directory Rules:
Each screen must follow:

graphql
Copy
Edit
/screens/[feature]/
  ├── @redux/           # Redux slices & hooks
  ├── @api/             # API wrappers or server actions
  ├── @context/         # Optional context for feature
  ├── components/       # UI components for the feature
  ├── utils/            # Optional local helpers
  └── screen.tsx        # Feature's main page component


#### app/ Directory Usage:
Contains only page.tsx, layout.tsx, and metadata files.

Should never contain business logic, state, or data fetching logic.

All routes must map to screens/[feature]/screen.tsx.



#### 🔁 Rules:

* Each page/feature **must be isolated** into its own folder inside `app/`.
* Use **modular organization** for logic & structure:

  * `@redux` for slice + hooks
  * `@api` for API logic
  * `@context` for React Context logic
* Every React component folder should follow:

  ```
  /[component_name]/index.tsx
  ```
* **Keep all files under 200–250 lines**. Break into sub-components as needed.

---

### 2. ⚙️ **Rendering Strategy: Client vs Server**

#### ✅ Prefer Server Components (`.tsx`) whenever possible:

* Use for:

  * Initial UI shell
  * Initial API fetch (using `getServerSideProps`, `fetch()`, or Server Actions)
  * Page layouts
  * Static/SEO-heavy content

#### 🧠 Use Client Components (`'use client'`) only for:

* Forms, event handlers, animations
* Interactive elements: dropdowns, modals, sliders

---

### 3. 🔌 **API Integration Strategy**

#### ⛓️ Integration Rules:

* All APIs should be called **from server** if possible (SSR, RSCs, or Server Actions).
* Use `Next.js Server Actions` where available for:

  * Mutations (POST/PUT/DELETE)
  * Prefetching initial data
* API functions should be abstracted in `@api` folder using wrappers like:

```ts
// app/dashboard/@api/fetchCases.ts
export const fetchCases = async () => {
  const res = await fetch(`${process.env.API_URL}/cases`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  return res.json();
};
```

---

### 4. 🧵 **State Management**

* Use `@redux/toolkit` in `@redux` folder of each feature
* Persist state via `redux-persist` where needed (auth, settings)
* For lightweight local state, use React Context in `@context`

---

### 5. 🎨 **UI & Animation Guidelines**

#### 📦 UI Libraries

* Use **ShadCN UI** for core components
* Use **Framer Motion** for animation transitions
* Use **Tailwind CSS** for consistent styling
* Prefer **SVGs or Lucide Icons** over PNGs for scalability

#### 🪄 Animation Rules

* Use Framer Motion for:

  * Page transitions
  * Modal and dropdown enter/exit
  * Button and hover interactions
* Avoid jank by limiting re-renders (use `memo`, `lazy`, `suspense`)

---

### 6. 🔍 **SEO & Performance Rules**

* Use `metadata` in each page (title, description, canonical)
* Use `<Suspense />` for async UI blocks
* Use `Image` from `next/image` for optimized images
* Use `loading="lazy"` and `priority` props where appropriate
* Enable automatic font optimization
* Implement prefetching via `<Link prefetch>`

---

### 7. 🔐 **Security Practices**

* Sanitize and validate all API inputs
* Avoid exposing env variables in client bundles
* Enforce strict CORS + content security policies
* Use middleware to validate headers or tokens if needed

---

### 8. 🧪 **Code Quality & Maintainability**

* ESLint + Prettier with strict rules
* Husky + Lint-staged for pre-commit hooks
* Use **Typescript strict mode**
* Every component should have:

  * Clear typings for props
  * Descriptive JSDoc for complex logic

---

### 9. 🧠 **Naming & Convention Standards**

* All component folders lowercase (e.g., `userCard`)
* Use PascalCase for components: `UserCard`, `Sidebar`
* Use camelCase for functions & variables: `fetchUserData`
* Prefix hooks: `useUserSettings`, `useScrollTrigger`

---

### 10. 🚀 Deployment & CI/CD Readiness

* Setup `env.local`, `env.production` for secrets
* Environment-aware base API URLs using `process.env.NEXT_PUBLIC_API_URL`
* SSR-safe code paths (avoid `window`, `localStorage` in server)
* Add Vercel/Cloudflare/Fly.io support with caching rules

---

### ✅ Augment Code Agent Instructions (TL;DR Summary)

> “This is a **modular Next.js 14+ project** using the **app/ directory** and **Server Components by default**.
> All API logic lives in `@api`, state in `@redux`, and context in `@context` folders inside each feature module.
> Each file must stay under 250 lines and use the `[component]/index.tsx` structure.
> Use Server Actions or GraphQL for backend API integration.
> UI uses **ShadCN UI**, **Framer Motion**, and **Tailwind**.
> SEO, rendering optimization, and security best practices must be followed.”

---
#### CODING & STRUCTURE RULES
⛔ No file should exceed 250 lines.

✅ Split large logic/UI into components inside components/

✅ All UI components must follow:

pgsql
Copy
Edit
/component-name/index.tsx
/component-name/styles.module.css
✅ Feature-level API calls go in @api/index.ts (or multiple files if needed).

✅ Redux slices and hooks go inside @redux/.

✅ Local TS types should go in types.ts (optional).

#### UI DEVELOPMENT FLOW (FOR EACH FEATURE)
For every feature screen (e.g., login, dashboard, cases, etc.):

✅ Step 1: Create screen folder under /screens
bash
Copy
Edit
/screens/login/

#### AUTHENTICATION HANDLING (GATEWAY SUPPORT)
Access Token must be passed as Authorization: Bearer {token} from client

Store tokens in secure, HTTP-only cookies (set via backend or client)

Middleware (app/middleware.ts) must protect routes by checking cookies

Limit 2-device login by fingerprinting (backend handles this)

#### API ACCESS RULES
Base URL: process.env.NEXT_PUBLIC_API_URL

Use REST for Auth endpoints (/auth/*)

Use GraphQL for data features (/graphql) if available

Always include required headers: Authorization, Content-Type

#### PERFORMANCE & UX
✅ Server-render anything static: UI skeleton, SEO metadata

✅ Use loading states, skeletons, suspense where needed

✅ Use Framer Motion for transitions, modals, slide-ins

✅ Use ShadCN UI + TailwindCSS for design

✅ Use next/image for all images

✅ Use <Link prefetch /> for internal navigation


#### INTEGRATION BEST PRACTICES
Task	Location	Rule
API Call	@api/index.ts	Always wrapped in reusable function
Data Caching	React Query or RTK	Use where long polling or invalidation needed
Form Submission	Client Component	Use Redux for state or direct async functions
SSR Prefetch	Server Component	Use fetch() or Server Actions in future

#### TL;DR INSTRUCTION FOR AUGMENT CODE:
“Develop feature screens under screens/[feature].
Create @api, @redux, components, and screen.tsx.
Route through app/[feature]/page.tsx.
Use REST for Auth, GraphQL for data.
Keep files modular, UI animated, and performance optimized.
Maintain SSR-first logic where possible.
Respect Auth tokens and security rules provided.”

